# 默认环境配置文件
# 基于开发环境配置

# 基础配置
SECRET_KEY=*p0t3vzf-pad+7imw)cueezvag5nwswz7(#399%04cd#3k2&of
DEBUG=true
SCHEDULER_SWITCH=false
ENVIRONMENT=development

# ljfl_declare_db 数据库配置
LJFL_DECLARE_DB_NAME=ljfl_declare_db
LJFL_DECLARE_DB_USERNAME=root
LJFL_DECLARE_DB_PASSWORD=zhitongbr2019
LJFL_DECLARE_DB_HOST=**********
LJFL_DECLARE_DB_PORT=3306
LJFL_DECLARE_DB_CONN_MAX_AGE=0

# ljfl_db 数据库配置
LJFL_DB_NAME=ljfl_db_nonresident
LJFL_DB_USERNAME=root
LJFL_DB_PASSWORD=zhitongbr2019
LJFL_DB_HOST=**********
LJFL_DB_PORT=3306
LJFL_DB_CONN_MAX_AGE=0

# transport_db 数据库配置
TRANSPORT_DB_NAME=transport_db
TRANSPORT_DB_USERNAME=root
TRANSPORT_DB_PASSWORD=!Qaz2wsx
TRANSPORT_DB_HOST=***********
TRANSPORT_DB_PORT=3306
TRANSPORT_DB_CONN_MAX_AGE=0

# 缓存配置
CACHE_TYPE=redis
REDIS_LOCATION=redis://**********:6379/4
REDIS_PREFIX=apiLocal:NonResident
REDIS_MAX_CONNECTIONS=100

# 邮件配置
EMAIL_ENABLE=false
EMAIL_HOST=smtp.exmail.qq.com
EMAIL_PORT=465
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=6LEaNwKagmbGofXs
EMAIL_RECEIVER=<EMAIL>,<EMAIL>

# 认证配置
AUTH_APPID=*********
AUTH_APPSECRET=jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7
AUTH_HOST=dev-auth.ztbory.com

# 基础服务配置
BASE_HOST=***********:8568
OSS_HOST=filemanager.ztbory.com
QUALIFI_HOST=***********:8520
COMPANY_SYS_IP=http://***********:8520
CITY_IP=http://***********:8188

# 微信小程序配置
WECHAT_MEAPP_APPID=wx80cac5483ba66b18
WECHAT_MEAPP_APPSECRET=a5523efa65c04c82c01058fe2d2e3abf

# 加密配置
GM_SM4_KEY=8BFFFBA3F4FE441521B5A62D2F70C36D
SHIELD_PRIVATE_KEY=C1D69117B6E93AE194516BF1CFC33C9E7B2A379FF9AC47684E2C87AA66163EF3
SHIELD_PUBLIC_KEY=04F0D1AF00D6E0DDBBEE344A99A5924ABFD1125B704A2499735D40DF5019A2B33BEF4F111A3EA9383637AA2A75B92ACA4B92460C9DA7CE4C897FFEAC35412D467F

# 其他服务配置
WECHAT_PAY=http://***********:8595
TRANSPORT_COMPANY_SERVICE=http://127.0.0.1
INTERNAL_NETWORKS=[]
