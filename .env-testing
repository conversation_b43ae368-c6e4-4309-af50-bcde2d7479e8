# 测试环境配置文件
# 基于 Base/settings/testing.py 生成

# 基础配置
SECRET_KEY=*p0t3vzf-pad+7imw)cueezvag5nwswz7(#399%04cd#3k2&of
DEBUG=false
SCHEDULER_SWITCH=true
ENVIRONMENT=testing

# ljfl_declare_db 数据库配置
LJFL_DECLARE_DB_NAME=ljfl_declare_db
LJFL_DECLARE_DB_USERNAME=ljfldb
LJFL_DECLARE_DB_PASSWORD=ljfl_db20201210
LJFL_DECLARE_DB_HOST=***********
LJFL_DECLARE_DB_PORT=3306
LJFL_DECLARE_DB_CONN_MAX_AGE=0

# ljfl_db 数据库配置
LJFL_DB_NAME=ljfl_db
LJFL_DB_USERNAME=ljfldb
LJFL_DB_PASSWORD=ljfl_db20201210
LJFL_DB_HOST=***********
LJFL_DB_PORT=3306
LJFL_DB_CONN_MAX_AGE=0

# ljfl_db_replica 数据库配置
LJFL_DB_REPLICA_NAME=ljfl_db
LJFL_DB_REPLICA_USERNAME=ljfldb
LJFL_DB_REPLICA_PASSWORD=ljfl_db20201210
LJFL_DB_REPLICA_HOST=***********
LJFL_DB_REPLICA_PORT=3306
LJFL_DB_REPLICA_CONN_MAX_AGE=0

# transport_db 数据库配置
TRANSPORT_DB_NAME=transport_db
TRANSPORT_DB_USERNAME=ljfldb
TRANSPORT_DB_PASSWORD=ljfl_db20201210
TRANSPORT_DB_HOST=***********
TRANSPORT_DB_PORT=3306
TRANSPORT_DB_CONN_MAX_AGE=0

# solid 数据库配置
SOLID_NAME=solid_logistics
SOLID_USERNAME=solidlogistics
SOLID_PASSWORD=solidlogistics20200107
SOLID_HOST=***********
SOLID_PORT=3306
SOLID_CONN_MAX_AGE=0

# jfpt 数据库配置
JFPT_NAME=bj_jfpt_admin
JFPT_USERNAME=ljfldb
JFPT_PASSWORD=ljfl_db20201210
JFPT_HOST=***********
JFPT_PORT=3306
JFPT_CONN_MAX_AGE=0

# tidb_ljfl_db 数据库配置
TIDB_LJFL_DB_NAME=ljfl_db
TIDB_LJFL_DB_USERNAME=ljfldb
TIDB_LJFL_DB_PASSWORD=ljfl_db20201210
TIDB_LJFL_DB_HOST=***********
TIDB_LJFL_DB_PORT=3306
TIDB_LJFL_DB_CONN_MAX_AGE=0

# other_transport_db 数据库配置
OTHER_TRANSPORT_DB_NAME=other_transport_db
OTHER_TRANSPORT_DB_USERNAME=ljfldb
OTHER_TRANSPORT_DB_PASSWORD=ljfl_db20201210
OTHER_TRANSPORT_DB_HOST=***********
OTHER_TRANSPORT_DB_PORT=3306
OTHER_TRANSPORT_DB_CONN_MAX_AGE=0

# bagbreak_db 数据库配置
BAGBREAK_DB_NAME=bagbreak
BAGBREAK_DB_USERNAME=ljfldb
BAGBREAK_DB_PASSWORD=ljfl_db20201210
BAGBREAK_DB_HOST=***********
BAGBREAK_DB_PORT=3306
BAGBREAK_DB_CONN_MAX_AGE=0

# 缓存配置
CACHE_TYPE=redis
REDIS_LOCATION=redis://***********:6379/15
REDIS_PREFIX=api:test:resident_server
REDIS_MAX_CONNECTIONS=100

# 邮件配置
EMAIL_ENABLE=true
EMAIL_HOST=smtp.exmail.qq.com
EMAIL_PORT=465
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=6LEaNwKagmbGofXs
EMAIL_RECEIVER=<EMAIL>,<EMAIL>

# 认证配置
AUTH_APPID=*********
AUTH_APPSECRET=jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7
AUTH_HOST=***********:8296

# 基础服务配置
BASE_HOST=***********:8290
OSS_HOST=filemanager.ztbory.com
QUALIFI_HOST=***********:8520
COMPANY_SYS_IP=http://***********:8520
CITY_IP=http://************:8094

# 微信小程序配置
WECHAT_MEAPP_APPID=wx80cac5483ba66b18
WECHAT_MEAPP_APPSECRET=a5523efa65c04c82c01058fe2d2e3abf

# 加密配置
GM_SM4_KEY=8BFFFBA3F4FE441521B5A62D2F70C36D
SHIELD_PRIVATE_KEY=C1D69117B6E93AE194516BF1CFC33C9E7B2A379FF9AC47684E2C87AA66163EF3
SHIELD_PUBLIC_KEY=04F0D1AF00D6E0DDBBEE344A99A5924ABFD1125B704A2499735D40DF5019A2B33BEF4F111A3EA9383637AA2A75B92ACA4B92460C9DA7CE4C897FFEAC35412D467F

# 网络配置
INTERNAL_NETWORKS=["***********/24","************"]

# 其他服务配置
WECHAT_PAY=https://api.ztbory.com/dev/wechatpay-v3
TRANSPORT_COMPANY_SERVICE=http://dev-transportadmin.ztbory.com
ACCOUNT_SERVER_URL=http://*************:8581
