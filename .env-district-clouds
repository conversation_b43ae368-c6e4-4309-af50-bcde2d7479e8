# 区级云环境配置文件
# 基于 Base/settings/district_clouds.py 生成
# 注意：此环境需要设置以下环境变量：PROXY_IP, DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, REDIS_HOST, REDIS_PORT, REDIS_PASSWORD

# 基础配置
SECRET_KEY=*p0t3vzf-pad+7imw)cueezvag5nwswz7(#399%04cd#3k2&of
DEBUG=false
SCHEDULER_SWITCH=true
ENVIRONMENT=district_clouds

# 数据库配置 - 使用环境变量
# 需要设置以下环境变量：
# DB_HOST - 数据库主机地址
# DB_PORT - 数据库端口 (默认: 3306)
# DB_USER - 数据库用户名 (默认: cityzt)
# DB_PASSWORD - 数据库密码 (默认: Ct_ztbory20221226)

# ljfl_declare_db 数据库配置
LJFL_DECLARE_DB_NAME=ljfl_declare_db
LJFL_DECLARE_DB_USERNAME=${DB_USER:-cityzt}
LJFL_DECLARE_DB_PASSWORD=${DB_PASSWORD:-Ct_ztbory20221226}
LJFL_DECLARE_DB_HOST=${DB_HOST}
LJFL_DECLARE_DB_PORT=${DB_PORT:-3306}
LJFL_DECLARE_DB_CONN_MAX_AGE=100

# ljfl_db 数据库配置
LJFL_DB_NAME=ljfl_db
LJFL_DB_USERNAME=${DB_USER:-cityzt}
LJFL_DB_PASSWORD=${DB_PASSWORD:-Ct_ztbory20221226}
LJFL_DB_HOST=${DB_HOST}
LJFL_DB_PORT=${DB_PORT:-3306}
LJFL_DB_CONN_MAX_AGE=100

# ljfl_db_replica 数据库配置
LJFL_DB_REPLICA_NAME=ljfl_db
LJFL_DB_REPLICA_USERNAME=${DB_USER:-cityzt}
LJFL_DB_REPLICA_PASSWORD=${DB_PASSWORD:-Ct_ztbory20221226}
LJFL_DB_REPLICA_HOST=${DB_HOST}
LJFL_DB_REPLICA_PORT=${DB_PORT:-3306}
LJFL_DB_REPLICA_CONN_MAX_AGE=100

# transport_db 数据库配置
TRANSPORT_DB_NAME=transport_db
TRANSPORT_DB_USERNAME=${DB_USER:-cityzt}
TRANSPORT_DB_PASSWORD=${DB_PASSWORD:-Ct_ztbory20221226}
TRANSPORT_DB_HOST=${DB_HOST}
TRANSPORT_DB_PORT=${DB_PORT:-3306}
TRANSPORT_DB_CONN_MAX_AGE=100

# solid 数据库配置
SOLID_NAME=solid_logistics
SOLID_USERNAME=${DB_USER:-cityzt}
SOLID_PASSWORD=${DB_PASSWORD:-Ct_ztbory20221226}
SOLID_HOST=${DB_HOST}
SOLID_PORT=${DB_PORT:-3306}
SOLID_CONN_MAX_AGE=100

# jfpt 数据库配置
JFPT_NAME=bj_jfpt_admin
JFPT_USERNAME=${DB_USER:-cityzt}
JFPT_PASSWORD=${DB_PASSWORD:-Ct_ztbory20221226}
JFPT_HOST=${DB_HOST}
JFPT_PORT=${DB_PORT:-3306}
JFPT_CONN_MAX_AGE=0

# tidb_ljfl_db 数据库配置
TIDB_LJFL_DB_NAME=ljfl_db
TIDB_LJFL_DB_USERNAME=${DB_USER:-cityzt}
TIDB_LJFL_DB_PASSWORD=${DB_PASSWORD:-Ct_ztbory20221226}
TIDB_LJFL_DB_HOST=${DB_HOST}
TIDB_LJFL_DB_PORT=${DB_PORT:-3306}
TIDB_LJFL_DB_CONN_MAX_AGE=0

# other_transport_db 数据库配置
OTHER_TRANSPORT_DB_NAME=other_transport_db
OTHER_TRANSPORT_DB_USERNAME=${DB_USER:-cityzt}
OTHER_TRANSPORT_DB_PASSWORD=${DB_PASSWORD:-Ct_ztbory20221226}
OTHER_TRANSPORT_DB_HOST=${DB_HOST}
OTHER_TRANSPORT_DB_PORT=${DB_PORT:-3306}
OTHER_TRANSPORT_DB_CONN_MAX_AGE=0

# 缓存配置 - 使用环境变量
# 需要设置以下环境变量：
# REDIS_HOST - Redis主机地址
# REDIS_PORT - Redis端口 (默认: 16380)
# REDIS_PASSWORD - Redis密码 (默认: City-Ljfl&20230222)
CACHE_TYPE=redis
REDIS_LOCATION=redis://${REDIS_HOST}:${REDIS_PORT:-16380}/4
REDIS_PREFIX=apiProduction:collectManageAPPV3
REDIS_PASSWORD=${REDIS_PASSWORD:-City-Ljfl&20230222}
REDIS_MAX_CONNECTIONS=100

# 邮件配置
EMAIL_ENABLE=true
EMAIL_HOST=smtp.exmail.qq.com
EMAIL_PORT=465
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=6LEaNwKagmbGofXs
EMAIL_RECEIVER=<EMAIL>,<EMAIL>

# 认证配置
AUTH_APPID=*********
AUTH_APPSECRET=jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7
AUTH_HOST=${PROXY_IP}/inskip/v3/auth

# 代理配置 - 使用环境变量
# 需要设置环境变量：PROXY_IP
PROXY_IP_A=http://${PROXY_IP}
PROXY_IP_B=${PROXY_IP}

# 基础服务配置
BASE_HOST=${PROXY_IP}/ljflbasedata
OSS_HOST=filemanager.ztbory.com
QUALIFI_HOST=${PROXY_IP}/v3/transport
COMPANY_SYS_IP=http://${PROXY_IP}/v3/transport
CITY_IP=http://${PROXY_IP}/v3/city

# 微信小程序配置
WECHAT_MEAPP_APPID=wx80cac5483ba66b18
WECHAT_MEAPP_APPSECRET=a5523efa65c04c82c01058fe2d2e3abf

# 加密配置
GM_SM4_KEY=8BFFFBA3F4FE441521B5A62D2F70C36D
SHIELD_PRIVATE_KEY=C1D69117B6E93AE194516BF1CFC33C9E7B2A379FF9AC47684E2C87AA66163EF3
SHIELD_PUBLIC_KEY=04F0D1AF00D6E0DDBBEE344A99A5924ABFD1125B704A2499735D40DF5019A2B33BEF4F111A3EA9383637AA2A75B92ACA4B92460C9DA7CE4C897FFEAC35412D467F

# 网络配置
INTERNAL_NETWORKS=[]

# 其他服务配置
WECHAT_PAY=https://api.ztbory.com/wechatpay-v3
TRANSPORT_COMPANY_SERVICE=http://transportadmin.ztbory.com
