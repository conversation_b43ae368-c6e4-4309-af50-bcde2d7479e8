#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from . import *

DEBUG = env('DEBUG')

SCHEDULER_SWITCH = env('SCHEDULER_SWITCH')

ALLOWED_HOSTS = [
    '*'
]
DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('LJFL_DECLARE_DB_NAME'),
        'USER': env('LJFL_DECLARE_DB_USERNAME'),
        'HOST': env('LJFL_DECLARE_DB_HOST'),
        'PASSWORD': env('LJFL_DECLARE_DB_PASSWORD'),
        'PORT': env('LJFL_DECLARE_DB_PORT'),
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('LJFL_DB_NAME'),
        'USER': env('LJFL_DB_USERNAME'),
        'HOST': env('LJFL_DB_HOST'),
        'PASSWORD': env('LJFL_DB_PASSWORD'),
        'PORT': env('LJFL_DB_PORT'),
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'ljfl_db_replica': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('LJFL_DB_REPLICA_NAME'),
        'USER': env('LJFL_DB_REPLICA_USERNAME'),
        'HOST': env('LJFL_DB_REPLICA_HOST'),
        'PASSWORD': env('LJFL_DB_REPLICA_PASSWORD'),
        'PORT': env('LJFL_DB_REPLICA_PORT'),
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'transport_db': {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': env('TRANSPORT_DB_HOST'),
        'PORT': env('TRANSPORT_DB_PORT'),
        'USER': env('TRANSPORT_DB_USERNAME'),
        'PASSWORD': env('TRANSPORT_DB_PASSWORD'),
        'NAME': env('TRANSPORT_DB_NAME'),
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    'solid': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('SOLID_DB_NAME'),
        'USER': env('SOLID_DB_USERNAME'),
        'PASSWORD': env('SOLID_DB_PASSWORD'),
        'HOST': env('SOLID_DB_HOST'),
        'PORT': env('SOLID_DB_PORT'),
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': 100,
    },
    "jfpt": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": env('JFPT_DB_NAME'),
        "USER": env('JFPT_DB_USERNAME'),
        "HOST": env('JFPT_DB_HOST'),
        "PASSWORD": env('JFPT_DB_PASSWORD'),
        "PORT": env('JFPT_DB_PORT'),
    },
    "tidb_ljfl_db": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": env('TIDB_LJFL_DB_NAME'),
        "USER": env('TIDB_LJFL_DB_USERNAME'),
        "PASSWORD": env('TIDB_LJFL_DB_PASSWORD'),
        "HOST": env('TIDB_LJFL_DB_HOST'),
        "PORT": env('TIDB_LJFL_DB_PORT'),
        "OPTIONS": {"charset": "utf8mb4"},
    },
    "other_transport_db": {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': env('OTHER_TRANSPORT_DB_HOST'),
        'PORT': env('OTHER_TRANSPORT_DB_PORT'),
        'USER': env('OTHER_TRANSPORT_DB_USERNAME'),
        'PASSWORD': env('OTHER_TRANSPORT_DB_PASSWORD'),
        'NAME': env('OTHER_TRANSPORT_DB_NAME'),
        'OPTIONS': {'charset': 'utf8mb4'},
    },
    "bagbreak_db": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": env('BAGBREAK_DB_NAME'),
        "USER": env('BAGBREAK_DB_USERNAME'),
        "PASSWORD": env('BAGBREAK_DB_PASSWORD'),
        "PORT": env('BAGBREAK_DB_PORT'),
        "HOST": env('BAGBREAK_DB_HOST'),
        "OPTIONS": {"charset": "utf8mb4"},
    },
}

SENTINELS = env.list('SENTINELS')
DJANGO_REDIS_CONNECTION_FACTORY = "django_redis.pool.SentinelConnectionFactory"
# Enable the alternate connection factory.

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "KEY_PREFIX": env('REDIS_PREFIX'),
        # The hostname in LOCATION is the primary (service / master) name
        "LOCATION": env('REDIS_LOCATION'),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.SentinelClient",
            "SENTINELS": SENTINELS,
        },
    },
}

# 管理授权
AUTH_APPID = env('AUTH_APPID')
AUTH_APPSECRET = env('AUTH_APPSECRET')
AUTH_HOST = env('AUTH_HOST')

# 基础服务
BASE_HOST = env('BASE_HOST')

# OSS服务
OSS_HOST = env('OSS_HOST')

# 资质审核服务
QUALIFI_HOST = env('QUALIFI_HOST')

COMPANY_SYS_IP = env('COMPANY_SYS_IP')

# 市级服务
CITY_IP = env('CITY_IP')

INTERNAL_NETWORKS = env.list('INTERNAL_NETWORKS')
