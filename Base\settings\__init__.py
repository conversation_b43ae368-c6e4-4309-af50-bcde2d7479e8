#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

from utils import k8s_http_url

"""
Django settings for Base project.

Generated by 'django-admin startproject' using Django 2.1.14.

For more information on this file, see
https://docs.djangoproject.com/en/2.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.1/ref/settings/
"""

import os
import socket
from pathlib import Path
import environ
from .. import plugin

# Initialize environment variables
env = environ.Env(
    DEBUG=(bool, False),
    SCHEDULER_SWITCH=(bool, False),
)

# Read .env file
env.read_env(os.path.join(Path(__file__).resolve().parent.parent.parent, '.env'))


APPLICATION_NAME = "bj_collectmanage_admin"

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), './../../'))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY')

# 定时任务开关
SCHEDULER_SWITCH = env('SCHEDULER_SWITCH')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env('DEBUG')
COMPANY_TYPE = {'401': '事业单位', '402': '国有企业', '403': '集体企业', '404': '民营企业'}
TRANSPORT_TYPE = {1: '直收直运', 8: '直运清运', 2: '转运'}
POWER_TYPE = {1: '燃油', 2: '混动', 3: '新能源', 4: '烧气'}
RESON_DATA = {0: '重复填报', 1: '关店', 2: '统一信用代码输入错误', 3: '非厨余垃圾产生主体', 4: '其他问题'}

ALLOWED_HOSTS = ['*']

# 邮件通知
EMAIL_ENABLE = env('EMAIL_ENABLE')  # 邮件通知是否可用
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_USE_TLS = False  # 是否使用TLS安全传输协议
EMAIL_USE_SSL = env('EMAIL_USE_SSL')  # 是否使用SSL加密
EMAIL_HOST = env('EMAIL_HOST')
EMAIL_PORT = env('EMAIL_PORT')
EMAIL_HOST_USER = env('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD')
EMAIL_RECEIVER = env.list('EMAIL_RECEIVER')

# Application definition

INSTALLED_APPS = [
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.staticfiles',

    'django_mysql',
    'rest_framework',
    'gunicorn',
    'mongoengine',

    # apps
    'CollectManageApp',
    'MiniApp'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'Lic.core.ApiMiddleWare',
    "utils.middleware.ResponseFilterMiddleware",  # 添加响应过滤中间件

]

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

REST_FRAMEWORK = {
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
    ],
    # 权限验证
    'DEFAULT_PERMISSION_CLASSES': [
        'Lic.core.MyPermission',
        'Lic.core.CheckCodingPermission',
    ],
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    'DEFAULT_RENDERER_CLASSES': (
        'Lic.core.ApiJsonRender',
    ),
    # 过滤器
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter'
    ),
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S'
}

# URL禁止自动补充/
APPEND_SLASH = False

# 跨域支持
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = (
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
    'VIEW',
)
CORS_ALLOW_HEADERS = (
    'XMLHttpRequest',
    'X_FILENAME',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'Pragma',
)

ROOT_URLCONF = 'Base.urls'

WSGI_APPLICATION = 'Base.wsgi.application'

# Database
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases

# Mysql Connection
DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_declare_db',
        'USER': env('DB_USERNAME'),
        'HOST': env('MYSQL_SERVICE_HOST'),
        'PASSWORD': env('DB_PASSWORD'),
        'PORT': env('MYSQL_SERVICE_PORT'),
        'OPTIONS': {'charset': 'utf8mb4'}
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ljfl_db',
        'USER': env('DB_USERNAME'),
        'HOST': env('MYSQL_SERVICE_HOST'),
        'PASSWORD': env('DB_PASSWORD'),
        'PORT': env('MYSQL_SERVICE_PORT'),
        'OPTIONS': {'charset': 'utf8mb4'}
    }
}

DATABASE_ROUTERS = ['Base.db_router.DatabaseAppsRouter']
DJANGO_MYSQL_REWRITE_QUERIES = True

DATABASE_APPS_MAPPING = {
    'ljfl_db': 'ljfl_db',
    'ljfl_db_replica': 'ljfl_db_replica',
    'ljfl_declare_db': 'ljfl_declare_db',
    'transport_db': 'transport_db',
    'solid': 'solid',
    'jfpt': 'jfpt',
    'other_transport_db': 'other_transport_db'
}

SOUTH_TESTS_MIGRATE = False
# logging settings
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {  # 格式化
        'simple': {
            'format': '%(asctime)s %(levelname)6s - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'console': {
            'format': '%(asctime)s %(levelname)8s - %(message)s',
            # 'format': '[%(asctime)s][%(levelname)s] %(pathname)s %(lineno)d -> %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        }
    },
    'handlers': {  # 处理器
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'console'
        },
        'fileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'log.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
        'issuingFileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'issuing.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
        'schedulerFileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'scheduler.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
        'errorFileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'error.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
        # 'statsFileHandler': {
        #     'level': 'INFO',
        #     'class': 'logging.handlers.TimedRotatingFileHandler',
        #     'formatter': 'simple',
        #     'filename': os.path.join(BASE_DIR, 'log', 'stats.log'),
        #     'when': 'midnight',
        #     'encoding': 'utf8'
        # }
    },
    'loggers': {  # 记录器
        'django': {
            'handlers': ['fileHandler', 'console'],
            'level': 'INFO',
            'propagate': False
        },
        'issuing': {
            'handlers': ['issuingFileHandler'],
            'level': 'INFO',
            'propagate': False
        },
        'scheduler': {
            'handlers': ['schedulerFileHandler'],
            'level': 'INFO',
            'propagate': False
        },
        'errorLogger': {
            'handlers': ['errorFileHandler'],
            'level': 'INFO',
            'propagate': False
        },
        # 'stats': {
        #     'handlers': ['statsFileHandler'],
        #     'level': 'INFO',
        #     'propagate': False
        # },
        'django.db.backends': {
            'handlers': ['console',"fileHandler"],
            'level': 'DEBUG',
            'filter': ['require_debug_true'],
            'propagate': False,
        },
    },

}

# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
#         'LOCATION': 'unique-snowflake'
#     }
# }

# Password validation
# https://docs.djangoproject.com/en/2.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/2.1/topics/i18n/

LANGUAGE_CODE = 'zh-Hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = 'static'
# 管理授权
AUTH_APPID = env('AUTH_APPID')
AUTH_APPSECRET = env('AUTH_APPSECRET')
AUTH_HOST = k8s_http_url(env('BJ_AUTH_MANAGER_PORT'))

# 基础服务
BASE_HOST = env('BASE_HOST')

# OSS服务
OSS_HOST = env('OSS_HOST')

# 资质审核服务
QUALIFI_HOST = k8s_http_url(env('BJ_TRANSPORT_PORT'))

# 微信小程序配置
WECHAT_MEAPP_APPID = env('WECHAT_MEAPP_APPID')
WECHAT_MEAPP_APPSECRET = env('WECHAT_MEAPP_APPSECRET')

# 清运公司服务
COMPANY_SYS_IP = k8s_http_url(env('BJ_TRANSPORT_PORT'))

# 市级服务
CITY_IP = env('CITY_IP')

WECHAT_PAY = env('WECHAT_PAY')

# GM sm4 密钥
GM_SM4_KEY = env('GM_SM4_KEY')

hostname = socket.gethostname()
ENVIRONMENT = env('ENVIRONMENT')
TRANSPORT_COMPANY_SERVICE = env('TRANSPORT_COMPANY_SERVICE')


# 邮件配置已在上面统一配置，这里删除重复配置

SHIELD = {
    "cryptor": {
        "private_key": env('SHIELD_PRIVATE_KEY'),
        "public_key": env('SHIELD_PUBLIC_KEY'),
    },
}

# 地址配置
INTERNAL_NETWORKS = []

# TODO: 需要在 .env 文件中配置的环境变量
"""
必需的环境变量配置：

# 基础配置
SECRET_KEY=*p0t3vzf-pad+7imw)cueezvag5nwswz7(#399%04cd#3k2&of
DEBUG=true
SCHEDULER_SWITCH=false
ENVIRONMENT=testing

# 数据库配置
DB_USERNAME=root
DB_PASSWORD=Ztbr#2024
MYSQL_SERVICE_HOST=*************
MYSQL_SERVICE_PORT=3306

# 邮件配置
EMAIL_ENABLE=true
EMAIL_USE_TLS=false
EMAIL_USE_SSL=true
EMAIL_HOST=smtp.exmail.qq.com
EMAIL_PORT=465
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=6LEaNwKagmbGofXs
EMAIL_RECEIVER=<EMAIL>,<EMAIL>

# 认证配置
AUTH_APPID=*********
AUTH_APPSECRET=jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7

# 微信小程序配置
WECHAT_MEAPP_APPID=wx80cac5483ba66b18
WECHAT_MEAPP_APPSECRET=a5523efa65c04c82c01058fe2d2e3abf

# 加密配置
GM_SM4_KEY=8BFFFBA3F4FE441521B5A62D2F70C36D
SHIELD_PRIVATE_KEY=C1D69117B6E93AE194516BF1CFC33C9E7B2A379FF9AC47684E2C87AA66163EF3
SHIELD_PUBLIC_KEY=04F0D1AF00D6E0DDBBEE344A99A5924ABFD1125B704A2499735D40DF5019A2B33BEF4F111A3EA9383637AA2A75B92ACA4B92460C9DA7CE4C897FFEAC35412D467F

# 服务配置
BASE_HOST=***********:8568
OSS_HOST=filemanager.ztbory.com
CITY_IP=http://***********:8188
WECHAT_PAY=http://***********:8595
TRANSPORT_COMPANY_SERVICE=http://dev-transportadmin.ztbory.com

# K8S 服务配置 (tcp:// 会自动转换为 http://)
BJ_AUTH_MANAGER_PORT=tcp://*************:8296
BJ_TRANSPORT_PORT=tcp://*************:8520

缺少的 HTTP 服务配置（需要添加到 .env）：
- 需要确认各个服务的正确 HTTP 地址
- 部分服务可能需要根据环境（testing/production）使用不同的地址
"""
