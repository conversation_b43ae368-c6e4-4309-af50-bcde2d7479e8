#!/usr/bin/python
# coding: utf-8
from __future__ import absolute_import

"""
Django settings for Base project.

Generated by 'django-admin startproject' using Django 2.1.14.

For more information on this file, see
https://docs.djangoproject.com/en/2.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.1/ref/settings/
"""

import os
import socket
from pathlib import Path
import environ
from utils import k8s_http_url

# Initialize environment variables
env = environ.Env(
    DEBUG=(bool, False),
    SCHEDULER_SWITCH=(bool, False),
)

# Read .env file based on environment
environment = os.environ.get('DJANGO_SETTINGS_MODULE', 'development')
if 'production' in environment:
    env_file = '.env-production'
elif 'testing' in environment:
    env_file = '.env-testing'
elif 'development' in environment:
    env_file = '.env-development'
elif 'city_clouds' in environment:
    env_file = '.env-city-clouds'
elif 'district_clouds' in environment:
    env_file = '.env-district-clouds'
elif 'production_local' in environment:
    env_file = '.env-production-local'
elif 'testing_local' in environment:
    env_file = '.env-testing-local'
else:
    env_file = '.env'

env.read_env(os.path.join(Path(__file__).resolve().parent.parent, env_file))

APPLICATION_NAME = "bj_collectmanage_admin"

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), './../'))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY')

# 定时任务开关
SCHEDULER_SWITCH = env('SCHEDULER_SWITCH')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env('DEBUG')
COMPANY_TYPE = {'401': '事业单位', '402': '国有企业', '403': '集体企业', '404': '民营企业'}
TRANSPORT_TYPE = {1: '直收直运', 8: '直运清运', 2: '转运'}
POWER_TYPE = {1: '燃油', 2: '混动', 3: '新能源', 4: '烧气'}
RESON_DATA = {0: '重复填报', 1: '关店', 2: '统一信用代码输入错误', 3: '非厨余垃圾产生主体', 4: '其他问题'}

ALLOWED_HOSTS = ['*']

# 邮件通知
EMAIL_ENABLE = env('EMAIL_ENABLE')  # 邮件通知是否可用
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_USE_TLS = False  # 是否使用TLS安全传输协议
EMAIL_USE_SSL = True  # 是否使用SSL加密
EMAIL_HOST = env('EMAIL_HOST')
EMAIL_PORT = env('EMAIL_PORT')
EMAIL_HOST_USER = env('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD')
EMAIL_RECEIVER = env.list('EMAIL_RECEIVER')

# Application definition

INSTALLED_APPS = [
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.staticfiles',

    'django_mysql',
    'rest_framework',
    'gunicorn',
    'mongoengine',

    # apps
    'CollectManageApp',
    'MiniApp'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'Lic.core.ApiMiddleWare',
    "utils.middleware.ResponseFilterMiddleware",  # 添加响应过滤中间件

]

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

REST_FRAMEWORK = {
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
    ],
    # 权限验证
    'DEFAULT_PERMISSION_CLASSES': [
        'Lic.core.MyPermission',
        'Lic.core.CheckCodingPermission',
    ],
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    'DEFAULT_RENDERER_CLASSES': (
        'Lic.core.ApiJsonRender',
    ),
    # 过滤器
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter'
    ),
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S'
}

# URL禁止自动补充/
APPEND_SLASH = False

# 跨域支持
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = (
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
    'VIEW',
)
CORS_ALLOW_HEADERS = (
    'XMLHttpRequest',
    'X_FILENAME',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'Pragma',
)

ROOT_URLCONF = 'Base.urls'

WSGI_APPLICATION = 'Base.wsgi.application'

# Database
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases

# Mysql Connection
DATABASES = {
    'default': {},
    'ljfl_declare_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('LJFL_DECLARE_DB_NAME'),
        'USER': env('LJFL_DECLARE_DB_USERNAME'),
        'HOST': env('LJFL_DECLARE_DB_HOST'),
        'PASSWORD': env('LJFL_DECLARE_DB_PASSWORD'),
        'PORT': env('LJFL_DECLARE_DB_PORT'),
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': env('LJFL_DECLARE_DB_CONN_MAX_AGE', default=0),
    },
    'ljfl_db': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('LJFL_DB_NAME'),
        'USER': env('LJFL_DB_USERNAME'),
        'HOST': env('LJFL_DB_HOST'),
        'PASSWORD': env('LJFL_DB_PASSWORD'),
        'PORT': env('LJFL_DB_PORT'),
        'OPTIONS': {'charset': 'utf8mb4'},
        'CONN_MAX_AGE': env('LJFL_DB_CONN_MAX_AGE', default=0),
    }
}

# 可选数据库配置
optional_dbs = [
    'ljfl_db_replica', 'transport_db', 'solid', 'jfpt', 
    'tidb_ljfl_db', 'other_transport_db', 'bagbreak_db'
]

for db_name in optional_dbs:
    db_name_upper = db_name.upper()
    if env(f'{db_name_upper}_NAME', default=None):
        DATABASES[db_name] = {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': env(f'{db_name_upper}_NAME'),
            'USER': env(f'{db_name_upper}_USERNAME'),
            'HOST': env(f'{db_name_upper}_HOST'),
            'PASSWORD': env(f'{db_name_upper}_PASSWORD'),
            'PORT': env(f'{db_name_upper}_PORT'),
            'OPTIONS': {'charset': 'utf8mb4'},
            'CONN_MAX_AGE': env(f'{db_name_upper}_CONN_MAX_AGE', default=0),
        }

DATABASE_ROUTERS = ['Base.db_router.DatabaseAppsRouter']
DJANGO_MYSQL_REWRITE_QUERIES = True

DATABASE_APPS_MAPPING = {
    'ljfl_db': 'ljfl_db',
    'ljfl_db_replica': 'ljfl_db_replica',
    'ljfl_declare_db': 'ljfl_declare_db',
    'transport_db': 'transport_db',
    'solid': 'solid',
    'jfpt': 'jfpt',
    'other_transport_db': 'other_transport_db'
}

SOUTH_TESTS_MIGRATE = False

# Cache configuration
CACHE_TYPE = env('CACHE_TYPE', default='redis')

if CACHE_TYPE == 'redis':
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": env('REDIS_LOCATION'),
            'KEY_PREFIX': env('REDIS_PREFIX'),
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
                "PASSWORD": env('REDIS_PASSWORD', default=None),
                "CONNECTION_POOL_KWARGS": {"max_connections": env('REDIS_MAX_CONNECTIONS', default=100)},
            },
            "TIMEOUT": env('REDIS_TIMEOUT', default=None)
        }
    }
elif CACHE_TYPE == 'sentinel':
    SENTINELS = env.list('SENTINELS')
    DJANGO_REDIS_CONNECTION_FACTORY = "django_redis.pool.SentinelConnectionFactory"
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "KEY_PREFIX": env('REDIS_PREFIX'),
            "LOCATION": env('REDIS_LOCATION'),
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.SentinelClient",
                "SENTINELS": SENTINELS,
            },
        },
    }
elif CACHE_TYPE == 'locmem':
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': env('CACHE_LOCATION', default='unique-snowflake'),
            'TIMEOUT': env('CACHE_TIMEOUT', default=300),
            'OPTIONS': {
                'MAX_ENTRIES': env('CACHE_MAX_ENTRIES', default=1000)
            }
        }
    }

# logging settings
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {  # 格式化
        'simple': {
            'format': '%(asctime)s %(levelname)6s - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'console': {
            'format': '%(asctime)s %(levelname)8s - %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        }
    },
    'handlers': {  # 处理器
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'console'
        },
        'fileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'log.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
        'issuingFileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'issuing.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
        'schedulerFileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'scheduler.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
        'errorFileHandler': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'simple',
            'filename': os.path.join(BASE_DIR, 'log', 'error.log'),
            'when': 'midnight',
            'encoding': 'utf8'
        },
    },
    'loggers': {  # 记录器
        'django': {
            'handlers': ['fileHandler', 'console'],
            'level': 'INFO',
            'propagate': False
        },
        'issuing': {
            'handlers': ['issuingFileHandler'],
            'level': 'INFO',
            'propagate': False
        },
        'scheduler': {
            'handlers': ['schedulerFileHandler'],
            'level': 'INFO',
            'propagate': False
        },
        'errorLogger': {
            'handlers': ['errorFileHandler'],
            'level': 'INFO',
            'propagate': False
        },
        'django.db.backends': {
            'handlers': ['console', "fileHandler"],
            'level': 'DEBUG',
            'filter': ['require_debug_true'],
            'propagate': False,
        },
    },
}

# Password validation
# https://docs.djangoproject.com/en/2.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/2.1/topics/i18n/

LANGUAGE_CODE = 'zh-Hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = 'static'

# 管理授权
AUTH_APPID = env('AUTH_APPID')
AUTH_APPSECRET = env('AUTH_APPSECRET')
AUTH_HOST = env('AUTH_HOST')

# 基础服务
BASE_HOST = env('BASE_HOST')

# OSS服务
OSS_HOST = env('OSS_HOST')

# 资质审核服务
QUALIFI_HOST = env('QUALIFI_HOST')

# 微信小程序配置
WECHAT_MEAPP_APPID = env('WECHAT_MEAPP_APPID')
WECHAT_MEAPP_APPSECRET = env('WECHAT_MEAPP_APPSECRET')

# 清运公司服务
COMPANY_SYS_IP = env('COMPANY_SYS_IP')

# 市级服务
CITY_IP = env('CITY_IP')

WECHAT_PAY = env('WECHAT_PAY')

# GM sm4 密钥
GM_SM4_KEY = env('GM_SM4_KEY')

# 环境配置
hostname = socket.gethostname()
ENVIRONMENT = env('ENVIRONMENT')
TRANSPORT_COMPANY_SERVICE = env('TRANSPORT_COMPANY_SERVICE')

SHIELD = {
    "cryptor": {
        "private_key": env('SHIELD_PRIVATE_KEY'),
        "public_key": env('SHIELD_PUBLIC_KEY'),
    },
}

# 网络配置
INTERNAL_NETWORKS = env.list('INTERNAL_NETWORKS', default=[])

# 可选服务配置
ACCOUNT_SERVER_URL = env('ACCOUNT_SERVER_URL', default=None)

# 代理配置（用于云环境）
PROXY_IP_A = env('PROXY_IP_A', default=None)
PROXY_IP_B = env('PROXY_IP_B', default=None)
