# Django Settings 配置说明

## 概述

项目已将原来分散在 `Base/settings/` 目录下的多个配置文件合并为一个统一的 `Base/settings.py` 文件，并为每个环境生成了对应的 `.env` 配置文件。

## 配置文件结构

### 主配置文件
- `Base/settings.py` - 统一的 Django 配置文件，使用环境变量

### 环境配置文件
- `.env-default` - 默认开发环境配置
- `.env-development` - 开发环境配置
- `.env-testing` - 测试环境配置
- `.env-testing-local` - 本地测试环境配置
- `.env-production` - 生产环境配置
- `.env-production-local` - 本地生产环境配置
- `.env-city-clouds` - 市级云环境配置
- `.env-district-clouds` - 区级云环境配置

## 使用方法

### 1. 设置环境变量
通过设置 `DJANGO_SETTINGS_MODULE` 环境变量来指定使用哪个配置：

```bash
# 开发环境
export DJANGO_SETTINGS_MODULE=Base.settings

# 生产环境
export DJANGO_SETTINGS_MODULE=Base.settings
```

### 2. 选择对应的 .env 文件
系统会根据 `DJANGO_SETTINGS_MODULE` 的值自动选择对应的 .env 文件：

- 包含 `production` → `.env-production`
- 包含 `testing` → `.env-testing`
- 包含 `development` → `.env-development`
- 包含 `city_clouds` → `.env-city-clouds`
- 包含 `district_clouds` → `.env-district-clouds`
- 包含 `production_local` → `.env-production-local`
- 包含 `testing_local` → `.env-testing-local`
- 其他情况 → `.env`

### 3. 复制并修改配置文件
```bash
# 复制对应环境的配置文件
cp .env-production .env-production.local
# 修改配置文件中的敏感信息
vim .env-production.local
```

## 主要配置项说明

### 基础配置
- `SECRET_KEY` - Django 密钥
- `DEBUG` - 调试模式开关
- `SCHEDULER_SWITCH` - 定时任务开关
- `ENVIRONMENT` - 环境标识

### 数据库配置
每个数据库都有独立的配置变量：
```
# ljfl_declare_db 数据库
LJFL_DECLARE_DB_NAME=ljfl_declare_db
LJFL_DECLARE_DB_USERNAME=root
LJFL_DECLARE_DB_PASSWORD=password
LJFL_DECLARE_DB_HOST=localhost
LJFL_DECLARE_DB_PORT=3306
LJFL_DECLARE_DB_CONN_MAX_AGE=0
```

支持的数据库：
- `ljfl_declare_db` - 主要声明数据库
- `ljfl_db` - 主要业务数据库
- `ljfl_db_replica` - 只读副本数据库
- `transport_db` - 运输数据库
- `solid` - 固废数据库
- `jfpt` - 缴费平台数据库
- `tidb_ljfl_db` - TiDB 数据库
- `other_transport_db` - 其他运输数据库
- `bagbreak_db` - 破袋数据库

### 缓存配置
支持三种缓存类型：
```
# Redis 缓存
CACHE_TYPE=redis
REDIS_LOCATION=redis://localhost:6379/0
REDIS_PREFIX=api:prefix
REDIS_PASSWORD=password
REDIS_MAX_CONNECTIONS=100

# Redis Sentinel 缓存
CACHE_TYPE=sentinel
SENTINELS=["host1:port1","host2:port2"]
REDIS_LOCATION=redis://master/0
REDIS_PREFIX=api:prefix

# 内存缓存
CACHE_TYPE=locmem
CACHE_LOCATION=unique-snowflake
CACHE_TIMEOUT=300
CACHE_MAX_ENTRIES=1000
```

### 服务配置
- `AUTH_HOST` - 认证服务地址
- `BASE_HOST` - 基础服务地址
- `OSS_HOST` - 对象存储服务地址
- `QUALIFI_HOST` - 资质审核服务地址
- `COMPANY_SYS_IP` - 清运公司服务地址
- `CITY_IP` - 市级服务地址

### 云环境特殊配置
区级云环境 (district_clouds) 需要设置以下系统环境变量：
- `PROXY_IP` - 代理服务器IP
- `DB_HOST` - 数据库主机
- `DB_PORT` - 数据库端口
- `DB_USER` - 数据库用户名
- `DB_PASSWORD` - 数据库密码
- `REDIS_HOST` - Redis主机
- `REDIS_PORT` - Redis端口
- `REDIS_PASSWORD` - Redis密码

## 迁移指南

### 从旧配置迁移
1. 备份原有配置文件
2. 复制对应环境的 .env 文件
3. 修改配置文件中的具体值
4. 更新部署脚本使用新的配置方式

### 注意事项
1. 所有敏感信息（密码、密钥）都应该通过环境变量配置
2. 不同环境使用不同的 .env 文件
3. .env 文件不应该提交到版本控制系统
4. 生产环境的配置文件应该严格保密

## 故障排除

### 常见问题
1. **配置文件找不到** - 检查 DJANGO_SETTINGS_MODULE 环境变量
2. **数据库连接失败** - 检查数据库配置是否正确
3. **Redis 连接失败** - 检查缓存配置和 Redis 服务状态
4. **环境变量未生效** - 确认 .env 文件路径和格式正确

### 调试方法
```python
# 在 Django shell 中检查配置
python manage.py shell
>>> from django.conf import settings
>>> print(settings.DATABASES)
>>> print(settings.CACHES)
```
