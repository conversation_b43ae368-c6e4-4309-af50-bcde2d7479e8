# 本地生产环境配置文件
# 基于 Base/settings/production_local.py 生成

# 基础配置
SECRET_KEY=*p0t3vzf-pad+7imw)cueezvag5nwswz7(#399%04cd#3k2&of
DEBUG=true
SCHEDULER_SWITCH=false
ENVIRONMENT=production_local

# ljfl_declare_db 数据库配置
LJFL_DECLARE_DB_NAME=ljfl_declare_db
LJFL_DECLARE_DB_USERNAME=collectmanage
LJFL_DECLARE_DB_PASSWORD=Collectmanage_20220625
LJFL_DECLARE_DB_HOST=************
LJFL_DECLARE_DB_PORT=33306
LJFL_DECLARE_DB_CONN_MAX_AGE=0

# ljfl_db 数据库配置
LJFL_DB_NAME=ljfl_db
LJFL_DB_USERNAME=collectmanage
LJFL_DB_PASSWORD=Collectmanage_20220625
LJFL_DB_HOST=************
LJFL_DB_PORT=33306
LJFL_DB_CONN_MAX_AGE=0

# ljfl_db_replica 数据库配置
LJFL_DB_REPLICA_NAME=ljfl_db
LJFL_DB_REPLICA_USERNAME=collectmanage
LJFL_DB_REPLICA_PASSWORD=Collectmanage_20220625
LJFL_DB_REPLICA_HOST=************
LJFL_DB_REPLICA_PORT=33306
LJFL_DB_REPLICA_CONN_MAX_AGE=0

# transport_db 数据库配置
TRANSPORT_DB_NAME=transport_db
TRANSPORT_DB_USERNAME=transport
TRANSPORT_DB_PASSWORD=Transport_db20210727
TRANSPORT_DB_HOST=************
TRANSPORT_DB_PORT=33307
TRANSPORT_DB_CONN_MAX_AGE=0

# tidb_ljfl_db 数据库配置
TIDB_LJFL_DB_NAME=ljfl_db
TIDB_LJFL_DB_USERNAME=cityljfl
TIDB_LJFL_DB_PASSWORD=City_ljfl20221024
TIDB_LJFL_DB_HOST=************
TIDB_LJFL_DB_PORT=33310
TIDB_LJFL_DB_CONN_MAX_AGE=0

# 缓存配置
CACHE_TYPE=locmem
CACHE_LOCATION=unique-snowflake
CACHE_TIMEOUT=300
CACHE_MAX_ENTRIES=1000

# 邮件配置
EMAIL_ENABLE=true
EMAIL_HOST=smtp.exmail.qq.com
EMAIL_PORT=465
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=6LEaNwKagmbGofXs
EMAIL_RECEIVER=<EMAIL>,<EMAIL>

# 认证配置
AUTH_APPID=*********
AUTH_APPSECRET=jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7
AUTH_HOST=auth.ztbory.com

# 基础服务配置
BASE_HOST=***********:8290
OSS_HOST=filemanager.ztbory.com
QUALIFI_HOST=***********:8520
COMPANY_SYS_IP=http://***********:8520
CITY_IP=http://***********:8094

# 微信小程序配置
WECHAT_MEAPP_APPID=wx80cac5483ba66b18
WECHAT_MEAPP_APPSECRET=a5523efa65c04c82c01058fe2d2e3abf

# 加密配置
GM_SM4_KEY=8BFFFBA3F4FE441521B5A62D2F70C36D
SHIELD_PRIVATE_KEY=C1D69117B6E93AE194516BF1CFC33C9E7B2A379FF9AC47684E2C87AA66163EF3
SHIELD_PUBLIC_KEY=04F0D1AF00D6E0DDBBEE344A99A5924ABFD1125B704A2499735D40DF5019A2B33BEF4F111A3EA9383637AA2A75B92ACA4B92460C9DA7CE4C897FFEAC35412D467F

# 网络配置
INTERNAL_NETWORKS=["***********/24","************"]

# 其他服务配置
WECHAT_PAY=https://api.ztbory.com/wechatpay-v3
TRANSPORT_COMPANY_SERVICE=http://transportadmin.ztbory.com
